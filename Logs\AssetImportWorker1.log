Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.46f1 (fb93bc360d3a) revision 16487356'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 65205 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.46f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Trunk/Projects/UnityProjects/ArcheryGame/Archery
-logFile
Logs/AssetImportWorker1.log
-srvPort
13158
-job-worker-count
11
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/Trunk/Projects/UnityProjects/ArcheryGame/Archery
D:/Trunk/Projects/UnityProjects/ArcheryGame/Archery
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [32204]  Target information:

Player connection [32204]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4051671107 [EditorId] 4051671107 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32204]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 4051671107 [EditorId] 4051671107 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32204] Host joined multi-casting on [***********:54997]...
Player connection [32204] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.28 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 7.57 ms.
Initialize engine version: 6000.0.46f1 (fb93bc360d3a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Trunk/Projects/UnityProjects/ArcheryGame/Archery/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2882)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56284
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002935 seconds.
- Loaded All Assemblies, in  0.405 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.283 seconds
Domain Reload Profiling: 686ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (182ms)
		LoadAssemblies (112ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (177ms)
				TypeCache.ScanAssembly (159ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (284ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (248ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (42ms)
			ProcessInitializeOnLoadAttributes (136ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.068 seconds
Refreshing native plugins compatible for Editor in 2.61 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.366 seconds
Domain Reload Profiling: 2432ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (861ms)
		LoadAssemblies (666ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (274ms)
			TypeCache.Refresh (204ms)
				TypeCache.ScanAssembly (186ms)
			BuildScriptInfoCaches (59ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1367ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1228ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (335ms)
			ProcessInitializeOnLoadAttributes (674ms)
			ProcessInitializeOnLoadMethodAttributes (207ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.80 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 233 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8074 unused Assets / (7.2 MB). Loaded Objects now: 8692.
Memory consumption went from 199.8 MB to 192.6 MB.
Total: 8.880400 ms (FindLiveObjects: 0.553400 ms CreateObjectMapping: 0.357800 ms MarkObjects: 5.084200 ms  DeleteObjects: 2.884200 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.11 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.05 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.3 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 159.7 MB.
Total: 7.471700 ms (FindLiveObjects: 0.504800 ms CreateObjectMapping: 0.320300 ms MarkObjects: 3.854700 ms  DeleteObjects: 2.791100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.05 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.0 MB.
Total: 7.035400 ms (FindLiveObjects: 0.578600 ms CreateObjectMapping: 0.463200 ms MarkObjects: 3.777500 ms  DeleteObjects: 2.215400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.09 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (6.9 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.1 MB.
Total: 6.989300 ms (FindLiveObjects: 0.491200 ms CreateObjectMapping: 0.282100 ms MarkObjects: 4.037000 ms  DeleteObjects: 2.178000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.69 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.4 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 159.6 MB.
Total: 8.937100 ms (FindLiveObjects: 0.536200 ms CreateObjectMapping: 0.325500 ms MarkObjects: 5.177700 ms  DeleteObjects: 2.896200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.20 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.0 MB.
Total: 8.609800 ms (FindLiveObjects: 0.473800 ms CreateObjectMapping: 0.278000 ms MarkObjects: 5.519500 ms  DeleteObjects: 2.337200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.02 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.6 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 159.4 MB.
Total: 7.697100 ms (FindLiveObjects: 0.469200 ms CreateObjectMapping: 0.283500 ms MarkObjects: 4.135900 ms  DeleteObjects: 2.807000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.21 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.0 MB.
Total: 7.465700 ms (FindLiveObjects: 0.470600 ms CreateObjectMapping: 0.282700 ms MarkObjects: 4.270400 ms  DeleteObjects: 2.441100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.97 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.1 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 159.9 MB.
Total: 8.173000 ms (FindLiveObjects: 0.482200 ms CreateObjectMapping: 0.303600 ms MarkObjects: 4.980400 ms  DeleteObjects: 2.405900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.20 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.0 MB.
Total: 7.308900 ms (FindLiveObjects: 0.467400 ms CreateObjectMapping: 0.318500 ms MarkObjects: 4.203600 ms  DeleteObjects: 2.318300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.18 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.0 MB.
Total: 6.990900 ms (FindLiveObjects: 0.467200 ms CreateObjectMapping: 0.281100 ms MarkObjects: 3.959000 ms  DeleteObjects: 2.282400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.99 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.0 MB.
Total: 7.313100 ms (FindLiveObjects: 0.602200 ms CreateObjectMapping: 0.499300 ms MarkObjects: 3.840000 ms  DeleteObjects: 2.370800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.05 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.4 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 159.6 MB.
Total: 9.339100 ms (FindLiveObjects: 0.644900 ms CreateObjectMapping: 0.531600 ms MarkObjects: 5.451500 ms  DeleteObjects: 2.709900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.07 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (6.1 MB). Loaded Objects now: 8692.
Memory consumption went from 167.0 MB to 160.9 MB.
Total: 8.591500 ms (FindLiveObjects: 0.497200 ms CreateObjectMapping: 0.280300 ms MarkObjects: 5.547100 ms  DeleteObjects: 2.265500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.07 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.05 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8039 unused Assets / (7.1 MB). Loaded Objects now: 8676.
Memory consumption went from 167.0 MB to 159.8 MB.
Total: 34.658300 ms (FindLiveObjects: 0.846300 ms CreateObjectMapping: 0.296500 ms MarkObjects: 30.996300 ms  DeleteObjects: 2.517600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.830 seconds
Refreshing native plugins compatible for Editor in 2.44 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.790 seconds
Domain Reload Profiling: 2620ms
	BeginReloadAssembly (802ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (211ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (16ms)
		CreateAndSetChildDomain (253ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (959ms)
		LoadAssemblies (844ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (209ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (790ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (609ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (342ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.35 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8056 unused Assets / (7.2 MB). Loaded Objects now: 8684.
Memory consumption went from 170.7 MB to 163.5 MB.
Total: 7.735700 ms (FindLiveObjects: 0.510700 ms CreateObjectMapping: 0.348800 ms MarkObjects: 4.231800 ms  DeleteObjects: 2.643500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.24 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.2 MB). Loaded Objects now: 8685.
Memory consumption went from 170.8 MB to 163.6 MB.
Total: 8.079300 ms (FindLiveObjects: 0.542800 ms CreateObjectMapping: 0.318400 ms MarkObjects: 4.686000 ms  DeleteObjects: 2.531000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.082 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.790 seconds
Domain Reload Profiling: 1872ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (819ms)
		LoadAssemblies (697ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (208ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (183ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (790ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (611ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (214ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.72 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8057 unused Assets / (7.2 MB). Loaded Objects now: 8692.
Memory consumption went from 170.7 MB to 163.5 MB.
Total: 7.997200 ms (FindLiveObjects: 0.527600 ms CreateObjectMapping: 0.355300 ms MarkObjects: 4.299300 ms  DeleteObjects: 2.814100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.55 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.1 MB). Loaded Objects now: 8692.
Memory consumption went from 170.8 MB to 163.7 MB.
Total: 8.214000 ms (FindLiveObjects: 0.511500 ms CreateObjectMapping: 0.325300 ms MarkObjects: 4.536300 ms  DeleteObjects: 2.839900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.362 seconds
Refreshing native plugins compatible for Editor in 2.55 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.977 seconds
Domain Reload Profiling: 2339ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (1052ms)
		LoadAssemblies (907ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (246ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (223ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (977ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (759ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (237ms)
			ProcessInitializeOnLoadAttributes (414ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8057 unused Assets / (7.0 MB). Loaded Objects now: 8699.
Memory consumption went from 170.7 MB to 163.7 MB.
Total: 9.594200 ms (FindLiveObjects: 0.644700 ms CreateObjectMapping: 0.355500 ms MarkObjects: 5.357000 ms  DeleteObjects: 3.235200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.29 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.2 MB). Loaded Objects now: 8699.
Memory consumption went from 170.8 MB to 163.6 MB.
Total: 8.550800 ms (FindLiveObjects: 0.622900 ms CreateObjectMapping: 0.316800 ms MarkObjects: 4.790300 ms  DeleteObjects: 2.819700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.00 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.0 MB). Loaded Objects now: 8699.
Memory consumption went from 170.8 MB to 163.8 MB.
Total: 8.275800 ms (FindLiveObjects: 0.486600 ms CreateObjectMapping: 0.283300 ms MarkObjects: 4.648100 ms  DeleteObjects: 2.856900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 454086.302250 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValue.cs
  artifactKey: Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValue.cs using Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0583508f27e310f830b43005c90e581d') in 0.2748976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.37 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.0 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.4 MB.
Total: 7.802700 ms (FindLiveObjects: 0.603700 ms CreateObjectMapping: 0.388300 ms MarkObjects: 4.272800 ms  DeleteObjects: 2.537100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 44.260700 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValue.cs
  artifactKey: Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValue.cs using Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '457e951be29207eebf0d794e775b951a') in 0.0024011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.03 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.4 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.0 MB.
Total: 8.110500 ms (FindLiveObjects: 0.488500 ms CreateObjectMapping: 0.276500 ms MarkObjects: 4.317300 ms  DeleteObjects: 3.027100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 42.979699 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValue.cs
  artifactKey: Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValue.cs using Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80bada473a09cfa6a2d547a00d2eede6') in 0.0023735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.08 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.0 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.4 MB.
Total: 7.220900 ms (FindLiveObjects: 0.487200 ms CreateObjectMapping: 0.297700 ms MarkObjects: 3.883100 ms  DeleteObjects: 2.551700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.22 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.1 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.3 MB.
Total: 7.378600 ms (FindLiveObjects: 0.553300 ms CreateObjectMapping: 0.274700 ms MarkObjects: 3.703200 ms  DeleteObjects: 2.846600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.64 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.1 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.2 MB.
Total: 7.136000 ms (FindLiveObjects: 0.495200 ms CreateObjectMapping: 0.295100 ms MarkObjects: 3.861000 ms  DeleteObjects: 2.484000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.18 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.2 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.2 MB.
Total: 7.042000 ms (FindLiveObjects: 0.487100 ms CreateObjectMapping: 0.296100 ms MarkObjects: 3.762400 ms  DeleteObjects: 2.495500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 98.654669 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValue.cs
  artifactKey: Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValue.cs using Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d0d4d78638a80f82ef7f4e7f77012e3') in 0.0084094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.46 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.0 MB). Loaded Objects now: 8701.
Memory consumption went from 171.4 MB to 164.4 MB.
Total: 7.772200 ms (FindLiveObjects: 0.537400 ms CreateObjectMapping: 0.307900 ms MarkObjects: 4.309200 ms  DeleteObjects: 2.616500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.221 seconds
Refreshing native plugins compatible for Editor in 3.11 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.977 seconds
Domain Reload Profiling: 2199ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (961ms)
		LoadAssemblies (773ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (247ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (978ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (784ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (278ms)
			ProcessInitializeOnLoadAttributes (396ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.77 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8057 unused Assets / (8.4 MB). Loaded Objects now: 8708.
Memory consumption went from 171.3 MB to 162.9 MB.
Total: 12.236000 ms (FindLiveObjects: 0.537100 ms CreateObjectMapping: 0.380000 ms MarkObjects: 7.134700 ms  DeleteObjects: 4.183000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.93 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.1 MB). Loaded Objects now: 8708.
Memory consumption went from 171.4 MB to 164.3 MB.
Total: 135.470200 ms (FindLiveObjects: 0.769900 ms CreateObjectMapping: 0.344400 ms MarkObjects: 111.144800 ms  DeleteObjects: 23.209800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5509.554447 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValue.cs
  artifactKey: Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValue.cs using Guid(005b6eb4121709b4bb857a8a60d9c9cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48bc70fd1a1344fbb5d1e7319ca29b53') in 0.1148434 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.320 seconds
Refreshing native plugins compatible for Editor in 2.40 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.911 seconds
Domain Reload Profiling: 2232ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (924ms)
		LoadAssemblies (900ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (911ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (210ms)
			ProcessInitializeOnLoadAttributes (438ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.33 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8057 unused Assets / (7.1 MB). Loaded Objects now: 8715.
Memory consumption went from 171.3 MB to 164.2 MB.
Total: 7.515700 ms (FindLiveObjects: 0.574500 ms CreateObjectMapping: 0.313100 ms MarkObjects: 4.087200 ms  DeleteObjects: 2.540000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.09 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8040 unused Assets / (7.0 MB). Loaded Objects now: 8715.
Memory consumption went from 171.4 MB to 164.4 MB.
Total: 9.126100 ms (FindLiveObjects: 0.604800 ms CreateObjectMapping: 0.418400 ms MarkObjects: 5.688600 ms  DeleteObjects: 2.413300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.029 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.788 seconds
Domain Reload Profiling: 1818ms
	BeginReloadAssembly (175ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (787ms)
		LoadAssemblies (672ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (610ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (218ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.94 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8057 unused Assets / (7.0 MB). Loaded Objects now: 8722.
Memory consumption went from 171.3 MB to 164.3 MB.
Total: 6.767600 ms (FindLiveObjects: 0.461200 ms CreateObjectMapping: 0.305800 ms MarkObjects: 3.654300 ms  DeleteObjects: 2.345500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.08 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8041 unused Assets / (7.0 MB). Loaded Objects now: 8723.
Memory consumption went from 171.4 MB to 164.4 MB.
Total: 7.337300 ms (FindLiveObjects: 0.493700 ms CreateObjectMapping: 0.293500 ms MarkObjects: 4.226100 ms  DeleteObjects: 2.323000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.361 seconds
Refreshing native plugins compatible for Editor in 2.65 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.840 seconds
Domain Reload Profiling: 2201ms
	BeginReloadAssembly (227ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (1047ms)
		LoadAssemblies (899ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (231ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (841ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (204ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 3.80 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (7.1 MB). Loaded Objects now: 8730.
Memory consumption went from 171.3 MB to 164.2 MB.
Total: 9.559800 ms (FindLiveObjects: 0.571600 ms CreateObjectMapping: 0.353200 ms MarkObjects: 5.323300 ms  DeleteObjects: 3.310200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1249.372042 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValueExample.cs
  artifactKey: Guid(f432bc7b1b0d4c645b99369a466bdf98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValueExample.cs using Guid(f432bc7b1b0d4c645b99369a466bdf98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '40e657a354f8a881d81dd6dbc375081b') in 0.0309592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.28 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8041 unused Assets / (6.9 MB). Loaded Objects now: 8730.
Memory consumption went from 171.4 MB to 164.5 MB.
Total: 7.895500 ms (FindLiveObjects: 0.501100 ms CreateObjectMapping: 0.286100 ms MarkObjects: 4.706600 ms  DeleteObjects: 2.400700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.40 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8041 unused Assets / (7.2 MB). Loaded Objects now: 8730.
Memory consumption went from 171.4 MB to 164.2 MB.
Total: 9.669900 ms (FindLiveObjects: 0.524200 ms CreateObjectMapping: 0.292600 ms MarkObjects: 6.131800 ms  DeleteObjects: 2.719900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 255.489809 seconds.
  path: Assets/Scripts/A_Rewrite/Common/DynamicValueExample.cs
  artifactKey: Guid(f432bc7b1b0d4c645b99369a466bdf98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/A_Rewrite/Common/DynamicValueExample.cs using Guid(f432bc7b1b0d4c645b99369a466bdf98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67d683d2366ccf77f95d4de0a3340b37') in 0.0252803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.290 seconds
Refreshing native plugins compatible for Editor in 3.66 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.979 seconds
Domain Reload Profiling: 2271ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (1017ms)
		LoadAssemblies (847ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (250ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (223ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (980ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (747ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (247ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.61 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (7.5 MB). Loaded Objects now: 8737.
Memory consumption went from 171.3 MB to 163.9 MB.
Total: 9.011700 ms (FindLiveObjects: 0.638200 ms CreateObjectMapping: 0.520400 ms MarkObjects: 4.601200 ms  DeleteObjects: 3.249600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.967 seconds
Refreshing native plugins compatible for Editor in 2.68 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.945 seconds
Domain Reload Profiling: 1914ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (744ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (945ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (754ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (199ms)
			ProcessInitializeOnLoadAttributes (445ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 3.20 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (6.6 MB). Loaded Objects now: 8744.
Memory consumption went from 173.2 MB to 166.7 MB.
Total: 9.311800 ms (FindLiveObjects: 0.570700 ms CreateObjectMapping: 0.342500 ms MarkObjects: 5.327900 ms  DeleteObjects: 3.069300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.179 seconds
Refreshing native plugins compatible for Editor in 3.24 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.041 seconds
Domain Reload Profiling: 2221ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (911ms)
		LoadAssemblies (769ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (225ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (205ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1042ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (827ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (255ms)
			ProcessInitializeOnLoadAttributes (452ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 3.17 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.07 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (6.6 MB). Loaded Objects now: 8751.
Memory consumption went from 173.2 MB to 166.7 MB.
Total: 9.115900 ms (FindLiveObjects: 0.581400 ms CreateObjectMapping: 0.338100 ms MarkObjects: 5.079600 ms  DeleteObjects: 3.115500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.194 seconds
Refreshing native plugins compatible for Editor in 2.58 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.763 seconds
Domain Reload Profiling: 1958ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (913ms)
		LoadAssemblies (780ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (203ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (763ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (590ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (199ms)
			ProcessInitializeOnLoadAttributes (305ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (7.2 MB). Loaded Objects now: 8758.
Memory consumption went from 173.3 MB to 166.1 MB.
Total: 7.228000 ms (FindLiveObjects: 0.572000 ms CreateObjectMapping: 0.320700 ms MarkObjects: 3.884300 ms  DeleteObjects: 2.450200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.78 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8041 unused Assets / (7.4 MB). Loaded Objects now: 8758.
Memory consumption went from 173.4 MB to 166.0 MB.
Total: 10.077200 ms (FindLiveObjects: 0.542000 ms CreateObjectMapping: 0.364000 ms MarkObjects: 5.697900 ms  DeleteObjects: 3.472000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.055 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.721 seconds
Domain Reload Profiling: 1777ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (789ms)
		LoadAssemblies (691ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (722ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (183ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.99 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (6.9 MB). Loaded Objects now: 8765.
Memory consumption went from 175.2 MB to 168.3 MB.
Total: 6.892100 ms (FindLiveObjects: 0.476900 ms CreateObjectMapping: 0.290800 ms MarkObjects: 3.652200 ms  DeleteObjects: 2.471400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.745 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.716 seconds
Domain Reload Profiling: 2464ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (1352ms)
		LoadAssemblies (1033ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (428ms)
			TypeCache.Refresh (148ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (268ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (717ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (282ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.35 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (8.7 MB). Loaded Objects now: 8772.
Memory consumption went from 175.2 MB to 166.5 MB.
Total: 48.282100 ms (FindLiveObjects: 0.531400 ms CreateObjectMapping: 0.333200 ms MarkObjects: 4.130100 ms  DeleteObjects: 43.286500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.80 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8041 unused Assets / (7.3 MB). Loaded Objects now: 8772.
Memory consumption went from 175.3 MB to 168.0 MB.
Total: 10.601400 ms (FindLiveObjects: 0.576500 ms CreateObjectMapping: 0.327000 ms MarkObjects: 6.624900 ms  DeleteObjects: 3.071600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.782 seconds
Refreshing native plugins compatible for Editor in 2.51 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.876 seconds
Domain Reload Profiling: 2659ms
	BeginReloadAssembly (225ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (1470ms)
		LoadAssemblies (858ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (723ms)
			TypeCache.Refresh (408ms)
				TypeCache.ScanAssembly (20ms)
			BuildScriptInfoCaches (291ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (876ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (671ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (219ms)
			ProcessInitializeOnLoadAttributes (366ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.20 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8058 unused Assets / (7.0 MB). Loaded Objects now: 8779.
Memory consumption went from 175.2 MB to 168.2 MB.
Total: 7.580800 ms (FindLiveObjects: 0.523600 ms CreateObjectMapping: 0.313400 ms MarkObjects: 4.045300 ms  DeleteObjects: 2.697600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.66 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8042 unused Assets / (6.9 MB). Loaded Objects now: 8780.
Memory consumption went from 175.3 MB to 168.4 MB.
Total: 31.462800 ms (FindLiveObjects: 0.641400 ms CreateObjectMapping: 0.481800 ms MarkObjects: 5.529200 ms  DeleteObjects: 24.809200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.953 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.687 seconds
Domain Reload Profiling: 1641ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (737ms)
		LoadAssemblies (622ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (170ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8059 unused Assets / (10.1 MB). Loaded Objects now: 8787.
Memory consumption went from 175.2 MB to 165.2 MB.
Total: 56.158800 ms (FindLiveObjects: 0.481600 ms CreateObjectMapping: 0.287600 ms MarkObjects: 3.735900 ms  DeleteObjects: 51.652900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.66 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8042 unused Assets / (3.7 MB). Loaded Objects now: 8787.
Memory consumption went from 175.3 MB to 171.7 MB.
Total: 117.267900 ms (FindLiveObjects: 0.869200 ms CreateObjectMapping: 0.318100 ms MarkObjects: 45.041200 ms  DeleteObjects: 71.037900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.45 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8042 unused Assets / (9.0 MB). Loaded Objects now: 8787.
Memory consumption went from 175.3 MB to 166.3 MB.
Total: 14.268500 ms (FindLiveObjects: 0.882000 ms CreateObjectMapping: 0.408600 ms MarkObjects: 4.815500 ms  DeleteObjects: 8.160900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.07 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8042 unused Assets / (9.1 MB). Loaded Objects now: 8787.
Memory consumption went from 175.3 MB to 166.2 MB.
Total: 22.167200 ms (FindLiveObjects: 0.527200 ms CreateObjectMapping: 0.313000 ms MarkObjects: 3.814200 ms  DeleteObjects: 17.511900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.33 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8042 unused Assets / (7.4 MB). Loaded Objects now: 8787.
Memory consumption went from 175.3 MB to 168.0 MB.
Total: 7.501700 ms (FindLiveObjects: 0.495000 ms CreateObjectMapping: 0.314900 ms MarkObjects: 4.003700 ms  DeleteObjects: 2.687100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.14 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8043 unused Assets / (8.9 MB). Loaded Objects now: 8788.
Memory consumption went from 175.3 MB to 166.5 MB.
Total: 12.415100 ms (FindLiveObjects: 0.500000 ms CreateObjectMapping: 0.283800 ms MarkObjects: 3.712400 ms  DeleteObjects: 7.918400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.733 seconds
Refreshing native plugins compatible for Editor in 2.56 ms, found 5 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.862 seconds
Domain Reload Profiling: 2597ms
	BeginReloadAssembly (371ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (1268ms)
		LoadAssemblies (1045ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (355ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (323ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (862ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (659ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (216ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.41 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8060 unused Assets / (7.3 MB). Loaded Objects now: 8795.
Memory consumption went from 175.2 MB to 168.0 MB.
Total: 8.005900 ms (FindLiveObjects: 0.616800 ms CreateObjectMapping: 0.509100 ms MarkObjects: 4.202000 ms  DeleteObjects: 2.675900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.14 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.0 MB). Loaded Objects now: 8807.
Memory consumption went from 175.4 MB to 168.5 MB.
Total: 8.995500 ms (FindLiveObjects: 0.505100 ms CreateObjectMapping: 0.290600 ms MarkObjects: 5.698100 ms  DeleteObjects: 2.500800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.09 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8055 unused Assets / (7.7 MB). Loaded Objects now: 8807.
Memory consumption went from 175.4 MB to 167.7 MB.
Total: 14.510400 ms (FindLiveObjects: 0.519000 ms CreateObjectMapping: 0.287300 ms MarkObjects: 5.522200 ms  DeleteObjects: 8.180400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.38 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8071 unused Assets / (8.1 MB). Loaded Objects now: 8823.
Memory consumption went from 175.5 MB to 167.4 MB.
Total: 20.954000 ms (FindLiveObjects: 0.598200 ms CreateObjectMapping: 0.311400 ms MarkObjects: 9.031300 ms  DeleteObjects: 11.011400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.93 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.05 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8071 unused Assets / (7.2 MB). Loaded Objects now: 8823.
Memory consumption went from 175.5 MB to 168.3 MB.
Total: 14.211200 ms (FindLiveObjects: 1.070400 ms CreateObjectMapping: 0.517900 ms MarkObjects: 8.083700 ms  DeleteObjects: 4.537500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 8.85 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.06 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8071 unused Assets / (7.6 MB). Loaded Objects now: 8823.
Memory consumption went from 175.5 MB to 167.8 MB.
Total: 26.383800 ms (FindLiveObjects: 1.675100 ms CreateObjectMapping: 0.719500 ms MarkObjects: 16.040300 ms  DeleteObjects: 7.946200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.13 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8071 unused Assets / (7.3 MB). Loaded Objects now: 8823.
Memory consumption went from 175.5 MB to 168.2 MB.
Total: 9.924300 ms (FindLiveObjects: 0.522100 ms CreateObjectMapping: 0.285500 ms MarkObjects: 6.260700 ms  DeleteObjects: 2.854700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 11.724 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 5 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.993 seconds
Domain Reload Profiling: 12718ms
	BeginReloadAssembly (10543ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (1811ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (6103ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (1104ms)
		LoadAssemblies (1088ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (232ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (195ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (993ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (745ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (233ms)
			ProcessInitializeOnLoadAttributes (395ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.00 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8088 unused Assets / (7.9 MB). Loaded Objects now: 8830.
Memory consumption went from 175.5 MB to 167.6 MB.
Total: 34.313300 ms (FindLiveObjects: 0.600800 ms CreateObjectMapping: 0.440900 ms MarkObjects: 3.953400 ms  DeleteObjects: 29.317000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.48 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.4 MB). Loaded Objects now: 8831.
Memory consumption went from 175.6 MB to 168.2 MB.
Total: 9.205900 ms (FindLiveObjects: 0.519500 ms CreateObjectMapping: 0.291400 ms MarkObjects: 5.577300 ms  DeleteObjects: 2.816500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.61 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.0 MB). Loaded Objects now: 8831.
Memory consumption went from 175.6 MB to 168.7 MB.
Total: 7.547300 ms (FindLiveObjects: 0.519000 ms CreateObjectMapping: 0.294400 ms MarkObjects: 4.186100 ms  DeleteObjects: 2.546500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.69 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.2 MB). Loaded Objects now: 8831.
Memory consumption went from 175.6 MB to 168.5 MB.
Total: 7.051700 ms (FindLiveObjects: 0.503700 ms CreateObjectMapping: 0.293200 ms MarkObjects: 3.810300 ms  DeleteObjects: 2.444000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.169 seconds
Refreshing native plugins compatible for Editor in 2.34 ms, found 5 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.983 seconds
Domain Reload Profiling: 2154ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (910ms)
		LoadAssemblies (741ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (214ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (983ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (761ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (250ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 3.69 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8089 unused Assets / (7.2 MB). Loaded Objects now: 8838.
Memory consumption went from 175.5 MB to 168.4 MB.
Total: 9.524900 ms (FindLiveObjects: 0.571000 ms CreateObjectMapping: 1.275200 ms MarkObjects: 4.760500 ms  DeleteObjects: 2.917100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.73 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.04 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.2 MB). Loaded Objects now: 8838.
Memory consumption went from 175.7 MB to 168.4 MB.
Total: 12.727100 ms (FindLiveObjects: 0.937000 ms CreateObjectMapping: 0.393600 ms MarkObjects: 7.475400 ms  DeleteObjects: 3.919800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.147 seconds
Refreshing native plugins compatible for Editor in 2.28 ms, found 5 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.835 seconds
Domain Reload Profiling: 1983ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (812ms)
		LoadAssemblies (705ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (835ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (618ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (322ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.02 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8089 unused Assets / (7.0 MB). Loaded Objects now: 8845.
Memory consumption went from 175.6 MB to 168.5 MB.
Total: 7.460900 ms (FindLiveObjects: 0.581000 ms CreateObjectMapping: 0.473000 ms MarkObjects: 4.016600 ms  DeleteObjects: 2.389400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.98 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.0 MB). Loaded Objects now: 8845.
Memory consumption went from 175.7 MB to 168.6 MB.
Total: 9.329500 ms (FindLiveObjects: 0.502500 ms CreateObjectMapping: 0.301200 ms MarkObjects: 5.353600 ms  DeleteObjects: 3.171100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.061 seconds
Refreshing native plugins compatible for Editor in 2.10 ms, found 5 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.834 seconds
Domain Reload Profiling: 1896ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (802ms)
		LoadAssemblies (648ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (213ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (835ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (601ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (175ms)
			ProcessInitializeOnLoadAttributes (343ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.07 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8089 unused Assets / (7.3 MB). Loaded Objects now: 8852.
Memory consumption went from 175.6 MB to 168.3 MB.
Total: 7.816200 ms (FindLiveObjects: 0.543500 ms CreateObjectMapping: 0.306900 ms MarkObjects: 4.287200 ms  DeleteObjects: 2.677400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.04 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.1 MB). Loaded Objects now: 8852.
Memory consumption went from 175.7 MB to 168.6 MB.
Total: 7.746400 ms (FindLiveObjects: 0.506400 ms CreateObjectMapping: 0.299000 ms MarkObjects: 4.355800 ms  DeleteObjects: 2.584100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.999 seconds
Refreshing native plugins compatible for Editor in 2.69 ms, found 5 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.727 seconds
Domain Reload Profiling: 1727ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (738ms)
		LoadAssemblies (635ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (727ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (305ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.12 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8089 unused Assets / (6.8 MB). Loaded Objects now: 8859.
Memory consumption went from 175.6 MB to 168.8 MB.
Total: 7.673800 ms (FindLiveObjects: 0.532600 ms CreateObjectMapping: 0.313500 ms MarkObjects: 4.028100 ms  DeleteObjects: 2.797300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.52 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.05 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8072 unused Assets / (7.2 MB). Loaded Objects now: 8859.
Memory consumption went from 175.7 MB to 168.4 MB.
Total: 8.604400 ms (FindLiveObjects: 0.587900 ms CreateObjectMapping: 0.485500 ms MarkObjects: 5.027800 ms  DeleteObjects: 2.501700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.100 seconds
Refreshing native plugins compatible for Editor in 2.05 ms, found 5 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.704 seconds
Domain Reload Profiling: 1805ms
	BeginReloadAssembly (251ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (780ms)
		LoadAssemblies (697ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (538ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 5 plugins.
Preloading 1 native plugins for Editor in 0.03 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8089 unused Assets / (7.1 MB). Loaded Objects now: 8866.
Memory consumption went from 175.6 MB to 168.5 MB.
Total: 7.199400 ms (FindLiveObjects: 0.594300 ms CreateObjectMapping: 0.459100 ms MarkObjects: 3.688500 ms  DeleteObjects: 2.455500 ms)

Prepare: number of updated asset objects reloaded= 0
