using System;
using System.Collections.Generic;
using System.Linq;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Main container for a Statescript graph containing nodes and their connections
    /// </summary>
    [MemoryPackable]
    public sealed partial class StatescriptGraph
    {
        [MemoryPackInclude]
        public string Name { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public string Description { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public List<StatescriptNode> Nodes { get; set; } = new();
        
        [MemoryPackInclude]
        public List<StatescriptConnection> Connections { get; set; } = new();
        
        [MemoryPackInclude]
        public List<StatescriptVariable> Variables { get; set; } = new();
        
        // [MemoryPackInclude]
        // public Dictionary<string, object> Properties { get; set; } = new();

        /// <summary>
        /// Runtime-only data, not serialized
        /// </summary>
        [MemoryPackIgnore]
        public StatescriptContext Context { get; set; }
        
        [MemoryPackIgnore]
        public bool IsRunning { get; private set; }
        
        [MemoryPackIgnore]
        public StatescriptNode EntryNode { get; private set; }
        
        [MemoryPackIgnore]
        private Dictionary<int, StatescriptNode> _nodeMap = new();
        
        [MemoryPackIgnore]
        private Dictionary<int, List<StatescriptConnection>> _outgoingConnections = new();
        
        [MemoryPackIgnore]
        private Dictionary<int, List<StatescriptConnection>> _incomingConnections = new();

        [MemoryPackConstructor]
        public StatescriptGraph()
        {
        }

        public StatescriptGraph(string name)
        {
            Name = name;
        }

        /// <summary>
        /// Initialize the graph for runtime execution
        /// </summary>
        public void Initialize(StatescriptContext context)
        {
            Context = context;
            BuildRuntimeMaps();
            FindEntryNode();
            
            // Initialize all nodes
            foreach (var node in Nodes)
            {
                node.Initialize(this, context);
            }
        }

        /// <summary>
        /// Start executing the graph
        /// </summary>
        public void Start()
        {
            if (IsRunning) return;
            if (EntryNode == null)
            {
                Context?.LogError("No entry node found in graph");
                return;
            }

            IsRunning = true;
            EntryNode.Execute();
        }

        /// <summary>
        /// Stop executing the graph
        /// </summary>
        public void Stop()
        {
            if (!IsRunning) return;
            
            IsRunning = false;
            
            // Reset all nodes
            foreach (var node in Nodes)
            {
                node.Reset();
            }
        }

        /// <summary>
        /// Update the graph (called each frame)
        /// </summary>
        public void Update(float deltaTime)
        {
            if (!IsRunning) return;

            // Update all active nodes
            foreach (var node in Nodes)
            {
                if (node.IsActive)
                {
                    node.Update(deltaTime);
                }
            }
        }

        /// <summary>
        /// Add a node to the graph
        /// </summary>
        public void AddNode(StatescriptNode node)
        {
            if (node == null) return;
            
            Nodes.Add(node);
            if (_nodeMap != null)
            {
                _nodeMap[node.Id] = node;
            }
        }

        /// <summary>
        /// Remove a node from the graph
        /// </summary>
        public bool RemoveNode(int nodeId)
        {
            var node = GetNode(nodeId);
            if (node == null) return false;

            // Remove all connections involving this node
            Connections.RemoveAll(c => c.FromNodeId == nodeId || c.ToNodeId == nodeId);
            
            Nodes.Remove(node);
            _nodeMap.Remove(nodeId);
            _outgoingConnections.Remove(nodeId);
            _incomingConnections.Remove(nodeId);
            
            return true;
        }

        /// <summary>
        /// Get a node by ID
        /// </summary>
        public StatescriptNode GetNode(int nodeId)
        {
            if (_nodeMap.TryGetValue(nodeId, out var node))
            {
                return node;
            }
            return null;
        }

        /// <summary>
        /// Add a connection between two nodes
        /// </summary>
        public void AddConnection(StatescriptConnection connection)
        {
            if (connection == null) return;
            
            Connections.Add(connection);
            
            if (_outgoingConnections != null)
            {
                if (!_outgoingConnections.ContainsKey(connection.FromNodeId))
                    _outgoingConnections[connection.FromNodeId] = new List<StatescriptConnection>();
                _outgoingConnections[connection.FromNodeId].Add(connection);
            }
            
            if (_incomingConnections != null)
            {
                if (!_incomingConnections.ContainsKey(connection.ToNodeId))
                    _incomingConnections[connection.ToNodeId] = new List<StatescriptConnection>();
                _incomingConnections[connection.ToNodeId].Add(connection);
            }
        }

        /// <summary>
        /// Get all outgoing connections from a node
        /// </summary>
        public List<StatescriptConnection> GetOutgoingConnections(int nodeId)
        {
            if (_outgoingConnections != null && _outgoingConnections.TryGetValue(nodeId, out var connections))
            {
                return connections;
            }
            return new List<StatescriptConnection>();
        }

        public List<StatescriptConnection> GetIncomingConnections(int nodeId)
        {
            if (_incomingConnections != null && _incomingConnections.TryGetValue(nodeId, out var connections))
            {
                return connections;
            }
            return new List<StatescriptConnection>();
        }

        /// <summary>
        /// Get a variable by name
        /// </summary>
        public StatescriptVariable GetVariable(string name)
        {
            return Variables.FirstOrDefault(v => v.Name == name);
        }

        /// <summary>
        /// Set a variable value
        /// </summary>
        public void SetVariableValue<T>(string name, T value)
        {
            var variable = GetVariable(name);
            if (variable != null)
            {
                variable.SetValue(value);
            }
        }

        /// <summary>
        /// Get a variable value
        /// </summary>
        public T GetVariableValue<T>(string name, T defaultValue = default)
        {
            var variable = GetVariable(name);
            if (variable == null) return defaultValue;
            if (variable.TryGetValue<T>(out var value))
            {
                return value;
            }
            
            return defaultValue;
        }

        private void BuildRuntimeMaps()
        {
            _nodeMap.Clear();
            _outgoingConnections.Clear();
            _incomingConnections.Clear();

            // Build node map
            foreach (var node in Nodes)
            {
                _nodeMap[node.Id] = node;
            }

            // Build connection maps
            foreach (var connection in Connections)
            {
                if (!_outgoingConnections.ContainsKey(connection.FromNodeId))
                    _outgoingConnections[connection.FromNodeId] = new List<StatescriptConnection>();
                _outgoingConnections[connection.FromNodeId].Add(connection);

                if (!_incomingConnections.ContainsKey(connection.ToNodeId))
                    _incomingConnections[connection.ToNodeId] = new List<StatescriptConnection>();
                _incomingConnections[connection.ToNodeId].Add(connection);
            }
        }

        private void FindEntryNode()
        {
            EntryNode = Nodes.FirstOrDefault(n => n.NodeType == StatescriptNodeType.Entry);
        }

        /// <summary>
        /// Validate the graph for errors
        /// </summary>
        public List<string> Validate()
        {
            var errors = new List<string>();

            // Check for entry node
            var entryNodes = Nodes.Where(n => n.NodeType == StatescriptNodeType.Entry).ToList();
            if (entryNodes.Count == 0)
            {
                errors.Add("Graph must have an entry node");
            }
            else if (entryNodes.Count > 1)
            {
                errors.Add("Graph can only have one entry node");
            }

            // Check for orphaned nodes
            foreach (var node in Nodes)
            {
                if (node.NodeType != StatescriptNodeType.Entry)
                {
                    var hasIncoming = _incomingConnections.ContainsKey(node.Id) && _incomingConnections[node.Id].Count > 0;
                    if (!hasIncoming)
                    {
                        errors.Add($"Node '{node.Name}' (ID: {node.Id}) has no incoming connections");
                    }
                }
            }

            // Validate individual nodes
            foreach (var node in Nodes)
            {
                var nodeErrors = node.Validate();
                errors.AddRange(nodeErrors);
            }

            return errors;
        }
    }
}
