#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Property editor for Statescript connections
    /// </summary>
    public class StatescriptConnectionPropertyEditor : EditorWindow
    {
        private StatescriptConnection _selectedConnection;
        private StatescriptGraph _currentGraph;
        private ConnectionEditorData _connectionEditorData;
        private Vector2 _scrollPosition;
        private bool _hasChanges = false;

        public static void ShowEditor(StatescriptConnection connection, StatescriptGraph graph, ConnectionEditorData editorData)
        {
            var window = GetWindow<StatescriptConnectionPropertyEditor>("Connection Properties");
            window._selectedConnection = connection;
            window._currentGraph = graph;
            window._connectionEditorData = editorData;
            window._hasChanges = false;
            window.Show();
        }

        private void OnGUI()
        {
            if (_selectedConnection == null)
            {
                EditorGUILayout.HelpBox("No connection selected", MessageType.Info);
                return;
            }

            EditorGUILayout.Space();

            // Header
            var fromNode = _currentGraph?.GetNode(_selectedConnection.FromNodeId);
            var toNode = _currentGraph?.GetNode(_selectedConnection.ToNodeId);
            var connectionName = $"{fromNode?.Name ?? "Unknown"} → {toNode?.Name ?? "Unknown"}";
            EditorGUILayout.LabelField($"Connection Properties: {connectionName}", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

            // Basic properties
            DrawBasicProperties();
            EditorGUILayout.Space();

            // Visual properties
            DrawVisualProperties();
            EditorGUILayout.Space();

            // Custom properties
            DrawCustomProperties();

            EditorGUILayout.EndScrollView();

            // Buttons
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Apply") && _hasChanges)
            {
                ApplyChanges();
            }

            if (GUILayout.Button("Reset"))
            {
                _hasChanges = false;
                Repaint();
            }

            if (GUILayout.Button("Delete Connection"))
            {
                if (EditorUtility.DisplayDialog("Delete Connection", 
                    "Are you sure you want to delete this connection?", 
                    "Delete", "Cancel"))
                {
                    DeleteConnection();
                }
            }

            if (GUILayout.Button("Close"))
            {
                Close();
            }

            EditorGUILayout.EndHorizontal();

            if (_hasChanges)
            {
                EditorGUILayout.HelpBox("Properties have been modified. Click Apply to save changes.", MessageType.Warning);
            }
        }

        private void DrawBasicProperties()
        {
            EditorGUILayout.LabelField("Basic Properties", EditorStyles.boldLabel);

            // Connection ID
            EditorGUILayout.LabelField("ID", _selectedConnection.Id.ToString());

            // From/To nodes
            var fromNode = _currentGraph?.GetNode(_selectedConnection.FromNodeId);
            var toNode = _currentGraph?.GetNode(_selectedConnection.ToNodeId);
            
            EditorGUILayout.LabelField("From Node", fromNode?.Name ?? "Unknown");
            EditorGUILayout.LabelField("To Node", toNode?.Name ?? "Unknown");

            // Port names
            EditorGUI.BeginChangeCheck();
            
            var newFromPort = EditorGUILayout.TextField("From Port", _selectedConnection.FromPortName);
            if (newFromPort != _selectedConnection.FromPortName)
            {
                _selectedConnection.FromPortName = newFromPort;
                _hasChanges = true;
            }

            var newToPort = EditorGUILayout.TextField("To Port", _selectedConnection.ToPortName);
            if (newToPort != _selectedConnection.ToPortName)
            {
                _selectedConnection.ToPortName = newToPort;
                _hasChanges = true;
            }
        }

        private void DrawVisualProperties()
        {
            if (_connectionEditorData == null) return;

            EditorGUILayout.LabelField("Visual Properties", EditorStyles.boldLabel);

            EditorGUI.BeginChangeCheck();

            // Color
            var newColor = EditorGUILayout.ColorField("Color", _connectionEditorData.Color);
            if (newColor != _connectionEditorData.Color)
            {
                _connectionEditorData.Color = newColor;
                _hasChanges = true;
            }

            // Width
            var newWidth = EditorGUILayout.FloatField("Width", _connectionEditorData.Width);
            if (newWidth != _connectionEditorData.Width)
            {
                _connectionEditorData.Width = Mathf.Max(0.1f, newWidth);
                _hasChanges = true;
            }

            // Label
            var newShowLabel = EditorGUILayout.Toggle("Show Label", _connectionEditorData.ShowLabel);
            if (newShowLabel != _connectionEditorData.ShowLabel)
            {
                _connectionEditorData.ShowLabel = newShowLabel;
                _hasChanges = true;
            }

            if (_connectionEditorData.ShowLabel)
            {
                var newLabel = EditorGUILayout.TextField("Label Text", _connectionEditorData.Label);
                if (newLabel != _connectionEditorData.Label)
                {
                    _connectionEditorData.Label = newLabel;
                    _hasChanges = true;
                }
            }

            // Control points for bezier curves
            EditorGUILayout.LabelField("Control Points", EditorStyles.boldLabel);
            
            if (_connectionEditorData.ControlPoints.Count == 0)
            {
                if (GUILayout.Button("Add Control Points"))
                {
                    _connectionEditorData.ControlPoints.Add(Vector2.zero);
                    _connectionEditorData.ControlPoints.Add(Vector2.zero);
                    _hasChanges = true;
                }
            }
            else
            {
                for (int i = 0; i < _connectionEditorData.ControlPoints.Count; i++)
                {
                    EditorGUILayout.BeginHorizontal();
                    var newPoint = EditorGUILayout.Vector2Field($"Point {i + 1}", _connectionEditorData.ControlPoints[i]);
                    if (newPoint != _connectionEditorData.ControlPoints[i])
                    {
                        _connectionEditorData.ControlPoints[i] = newPoint;
                        _hasChanges = true;
                    }
                    
                    if (GUILayout.Button("X", GUILayout.Width(20)))
                    {
                        _connectionEditorData.ControlPoints.RemoveAt(i);
                        _hasChanges = true;
                        break;
                    }
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Point"))
                {
                    _connectionEditorData.ControlPoints.Add(Vector2.zero);
                    _hasChanges = true;
                }
                if (GUILayout.Button("Clear All"))
                {
                    _connectionEditorData.ControlPoints.Clear();
                    _hasChanges = true;
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        private void DrawCustomProperties()
        {
            EditorGUILayout.LabelField("Custom Properties", EditorStyles.boldLabel);

            if (_selectedConnection.Properties.Count == 0)
            {
                EditorGUILayout.LabelField("No custom properties");
                
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Add Property"))
                {
                    _selectedConnection.SetProperty("NewProperty", "");
                    _hasChanges = true;
                }
                EditorGUILayout.EndHorizontal();
                return;
            }

            var keysToRemove = new List<string>();
            var propertiesToUpdate = new Dictionary<string, object>();

            foreach (var kvp in _selectedConnection.Properties)
            {
                EditorGUILayout.BeginHorizontal();

                EditorGUILayout.LabelField(kvp.Key, GUILayout.Width(100));

                var valueStr = kvp.Value?.ToString() ?? "";
                var newValueStr = EditorGUILayout.TextField(valueStr);

                if (newValueStr != valueStr)
                {
                    propertiesToUpdate[kvp.Key] = newValueStr;
                    _hasChanges = true;
                }

                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    keysToRemove.Add(kvp.Key);
                    _hasChanges = true;
                }

                EditorGUILayout.EndHorizontal();
            }

            // Apply updates
            foreach (var kvp in propertiesToUpdate)
            {
                _selectedConnection.SetProperty(kvp.Key, kvp.Value);
            }

            // Remove properties
            foreach (var key in keysToRemove)
            {
                _selectedConnection.Properties.Remove(key);
            }

            // Add new property button
            EditorGUILayout.Space();
            if (GUILayout.Button("Add Property"))
            {
                _selectedConnection.SetProperty("NewProperty", "");
                _hasChanges = true;
            }
        }

        private void ApplyChanges()
        {
            _hasChanges = false;
            
            // Mark the graph as dirty
            if (_currentGraph != null)
            {
                EditorUtility.SetDirty(_currentGraph as UnityEngine.Object);
            }
            
            Repaint();
        }

        private void DeleteConnection()
        {
            if (_currentGraph != null && _selectedConnection != null)
            {
                _currentGraph.RemoveConnection(_selectedConnection);
                EditorUtility.SetDirty(_currentGraph as UnityEngine.Object);
            }
            Close();
        }
    }
}
#endif
